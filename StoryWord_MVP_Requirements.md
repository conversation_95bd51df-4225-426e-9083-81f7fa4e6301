# StoryWord MVP 产品需求文档

## 文档信息
- **产品名称**: StoryWord - 英语单词情境化记忆平台
- **文档版本**: v1.0
- **创建日期**: 2024年
- **更新日期**: 2024年
- **负责人**: 产品团队
- **目标受众**: 开发团队

## 目录
1. [产品概述](#产品概述)
2. [技术架构](#技术架构)
3. [数据库设计](#数据库设计)
4. [API接口规范](#api接口规范)
5. [功能模块详细规格](#功能模块详细规格)
6. [用户界面规范](#用户界面规范)
7. [开发规范](#开发规范)
8. [测试要求](#测试要求)
9. [部署配置](#部署配置)

---

## 产品概述

### 1.1 产品定位
StoryWord是一个基于AI协作创作的英语单词情境化记忆学习平台，通过用户参与故事创作的方式，将枯燥的单词记忆转化为有趣的创意体验。

### 1.2 核心功能
- 智能英语水平评估系统
- AI协作式故事创建引擎
- 个性化词汇推荐系统
- 智能复习调度系统
- 多模态学习体验

### 1.3 技术栈
- **前端**: React 18 + TypeScript + Tailwind CSS
- **后端**: Node.js + Express + TypeScript
- **数据库**: PostgreSQL + Redis
- **AI服务**: OpenAI API (GPT-4, DALL-E)
- **部署**: Docker + AWS/阿里云

---

## 技术架构

### 2.1 系统架构图
```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│                     │    │                     │    │                     │
│    Frontend Web     │────│   Backend API       │────│   Database Layer    │
│    (React + TS)     │    │  (Node.js + Express)│    │  (PostgreSQL+Redis) │
│                     │    │                     │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                     │
                           ┌─────────────────────┐
                           │                     │
                           │   External APIs     │
                           │  (OpenAI, DALL-E)   │
                           │                     │
                           └─────────────────────┘
```

### 2.2 模块架构
```
src/
├── frontend/
│   ├── components/          # React组件
│   ├── pages/              # 页面组件
│   ├── hooks/              # 自定义Hook
│   ├── services/           # API调用服务
│   ├── store/              # 状态管理
│   ├── types/              # TypeScript类型定义
│   └── utils/              # 工具函数
├── backend/
│   ├── controllers/        # 控制器
│   ├── services/           # 业务逻辑
│   ├── models/             # 数据模型
│   ├── middleware/         # 中间件
│   ├── routes/             # 路由配置
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
└── shared/
    ├── types/              # 共享类型定义
    └── constants/          # 常量定义
```

---

## 数据库设计

### 3.1 数据表结构

#### 3.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    cefr_level VARCHAR(2) DEFAULT 'A1', -- A1, A2, B1, B2, C1, C2
    profile_picture TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);
```

#### 3.1.2 词汇表 (vocabulary)
```sql
CREATE TABLE vocabulary (
    id SERIAL PRIMARY KEY,
    word VARCHAR(100) NOT NULL,
    phonetic VARCHAR(200),
    cefr_level VARCHAR(2) NOT NULL,
    frequency_rank INTEGER,
    part_of_speech VARCHAR(20),
    definition_en TEXT NOT NULL,
    definition_cn TEXT NOT NULL,
    example_sentences JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.1.3 用户学习进度 (user_progress)
```sql
CREATE TABLE user_progress (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    vocabulary_id INTEGER REFERENCES vocabulary(id),
    mastery_level INTEGER DEFAULT 0, -- 0-5 掌握程度
    first_learned_at TIMESTAMP,
    last_reviewed_at TIMESTAMP,
    review_count INTEGER DEFAULT 0,
    success_rate FLOAT DEFAULT 0,
    next_review_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.1.4 故事表 (stories)
```sql
CREATE TABLE stories (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    vocabulary_ids INTEGER[] NOT NULL,
    story_type VARCHAR(50), -- adventure, daily, business, etc.
    difficulty_level VARCHAR(2),
    word_count INTEGER,
    image_url TEXT,
    audio_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.1.5 学习会话 (learning_sessions)
```sql
CREATE TABLE learning_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_type VARCHAR(50), -- daily_learning, review, assessment
    vocabulary_ids INTEGER[],
    story_id INTEGER REFERENCES stories(id),
    duration_minutes INTEGER,
    completed_at TIMESTAMP,
    performance_score FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 索引设计
```sql
-- 用户相关索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- 词汇相关索引
CREATE INDEX idx_vocabulary_word ON vocabulary(word);
CREATE INDEX idx_vocabulary_cefr_level ON vocabulary(cefr_level);
CREATE INDEX idx_vocabulary_frequency ON vocabulary(frequency_rank);

-- 学习进度索引
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_user_progress_next_review ON user_progress(next_review_at);

-- 故事相关索引
CREATE INDEX idx_stories_user_id ON stories(user_id);
CREATE INDEX idx_stories_type ON stories(story_type);
```

### 3.3 Redis缓存设计
```typescript
// 缓存键名规范
interface CacheKeys {
  userSession: `session:${string}`;           // 用户会话
  vocabularyByLevel: `vocab:${string}`;       // 按级别的词汇
  userProgress: `progress:${number}`;         // 用户学习进度
  storyContent: `story:${number}`;           // 故事内容
  aiResponse: `ai:${string}`;                // AI响应缓存
}

// 缓存过期时间
const CACHE_TTL = {
  userSession: 3600 * 24,      // 24小时
  vocabularyByLevel: 3600 * 4, // 4小时
  userProgress: 3600,          // 1小时
  storyContent: 3600 * 2,      // 2小时
  aiResponse: 3600 * 12        // 12小时
};
```

---

## API接口规范

### 4.1 接口通用规范

#### 4.1.1 请求格式
```typescript
// 请求头
interface RequestHeaders {
  'Content-Type': 'application/json';
  'Authorization': `Bearer ${string}`;
  'X-API-Version': '1.0';
}

// 通用响应格式
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

#### 4.1.2 错误码规范
```typescript
enum ErrorCodes {
  // 认证相关
  UNAUTHORIZED = 'AUTH_001',
  TOKEN_EXPIRED = 'AUTH_002',
  INVALID_TOKEN = 'AUTH_003',
  
  // 用户相关
  USER_NOT_FOUND = 'USER_001',
  EMAIL_ALREADY_EXISTS = 'USER_002',
  INVALID_CREDENTIALS = 'USER_003',
  
  // 业务逻辑
  VOCABULARY_NOT_FOUND = 'VOCAB_001',
  STORY_GENERATION_FAILED = 'STORY_001',
  ASSESSMENT_INCOMPLETE = 'ASSESS_001',
  
  // 系统错误
  INTERNAL_SERVER_ERROR = 'SYS_001',
  DATABASE_ERROR = 'SYS_002',
  EXTERNAL_API_ERROR = 'SYS_003'
}
```

### 4.2 认证模块 API

#### 4.2.1 用户注册
```typescript
POST /api/auth/register
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123"
}

Response: {
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "username",
      "cefr_level": "A1"
    },
    "token": "jwt_token_here"
  }
}
```

#### 4.2.2 用户登录
```typescript
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

Response: {
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "username",
      "cefr_level": "B1"
    },
    "token": "jwt_token_here"
  }
}
```

### 4.3 水平评估模块 API

#### 4.3.1 获取评估题目
```typescript
GET /api/assessment/questions?level=A1&count=15

Response: {
  "success": true,
  "data": {
    "questions": [
      {
        "id": 1,
        "word": "hello",
        "options": ["你好", "再见", "谢谢", "不客气"],
        "correct_index": 0,
        "difficulty": "A1"
      }
    ],
    "session_id": "assessment_session_123"
  }
}
```

#### 4.3.2 提交评估答案
```typescript
POST /api/assessment/submit
{
  "session_id": "assessment_session_123",
  "answers": [
    {
      "question_id": 1,
      "selected_index": 0,
      "time_taken": 3.5
    }
  ]
}

Response: {
  "success": true,
  "data": {
    "cefr_level": "B1",
    "score": 85,
    "accuracy": 0.85,
    "recommended_vocabulary": ["advance", "develop", "system"]
  }
}
```

### 4.4 词汇推荐模块 API

#### 4.4.1 获取每日词汇
```typescript
GET /api/vocabulary/daily?level=B1&count=20

Response: {
  "success": true,
  "data": {
    "vocabulary": [
      {
        "id": 1,
        "word": "develop",
        "phonetic": "/dɪˈveləp/",
        "cefr_level": "B1",
        "part_of_speech": "verb",
        "definition_en": "to grow or change into something bigger, stronger, or more advanced",
        "definition_cn": "发展，开发",
        "example_sentences": [
          "The company plans to develop new products.",
          "Children develop quickly in their first year."
        ]
      }
    ],
    "learning_plan": {
      "current_level_words": 14,
      "next_level_words": 6,
      "review_words": 3
    }
  }
}
```

### 4.5 单词学习模块 API

#### 4.5.1 开始单词学习会话
```typescript
POST /api/vocabulary/start-learning
{
  "vocabulary_ids": [1, 2, 3, 4, 5]
}

Response: {
  "success": true,
  "data": {
    "session_id": "vocab_session_456",
    "vocabulary_list": [
      {
        "id": 1,
        "word": "develop",
        "phonetic": "/dɪˈveləp/",
        "definition_cn": "发展，开发",
        "cefr_level": "B1"
      }
    ],
    "total_words": 5,
    "estimated_time": 15
  }
}
```

#### 4.5.2 处理用户造句
```typescript
POST /api/vocabulary/process-sentence
{
  "session_id": "vocab_session_456",
  "word_id": 1,
  "user_sentence": "I want to develop my programming skills",
  "word": "develop"
}

Response: {
  "success": true,
  "data": {
    "corrected_sentence": "I want to develop my programming skills further.",
    "corrections": [
      {
        "type": "enhancement",
        "original": "I want to develop my programming skills",
        "corrected": "I want to develop my programming skills further",
        "explanation": "Added 'further' to make the sentence more complete and natural"
      }
    ],
    "grammar_feedback": {
      "score": 85,
      "strengths": ["Correct word usage", "Good sentence structure"],
      "improvements": ["Consider adding more details"]
    },
    "next_word_available": true
  }
}
```

#### 4.5.3 获取下一个单词
```typescript
POST /api/vocabulary/next-word
{
  "session_id": "vocab_session_456"
}

Response: {
  "success": true,
  "data": {
    "word": {
      "id": 2,
      "word": "analyze",
      "phonetic": "/ˈænəlaɪz/",
      "definition_cn": "分析",
      "cefr_level": "B1"
    },
    "progress": {
      "current_index": 2,
      "total_words": 5,
      "completed_words": 1
    }
  }
}
```

#### 4.5.4 完成学习会话
```typescript
POST /api/vocabulary/complete-session
{
  "session_id": "vocab_session_456"
}

Response: {
  "success": true,
  "data": {
    "session_summary": {
      "total_words": 5,
      "completed_words": 5,
      "average_score": 87,
      "time_spent": 12,
      "sentences_created": 5
    },
    "learning_progress": {
      "words_mastered": 3,
      "words_need_review": 2,
      "next_review_date": "2024-01-16T10:00:00Z"
    },
    "achievements": [
      {
        "type": "streak",
        "title": "连续学习3天",
        "description": "保持学习热情！"
      }
    ]
  }
}
```

### 4.6 学习进度模块 API

#### 4.6.1 获取学习进度
```typescript
GET /api/progress/overview

Response: {
  "success": true,
  "data": {
    "current_level": "B1",
    "words_learned": 245,
    "stories_created": 12,
    "daily_streak": 7,
    "weekly_stats": {
      "total_time": 180,
      "words_reviewed": 65,
      "accuracy_rate": 0.87
    },
    "upcoming_reviews": [
      {
        "vocabulary_id": 1,
        "word": "develop",
        "due_at": "2024-01-15T10:00:00Z",
        "priority": "high"
      }
    ]
  }
}
```

#### 4.6.2 更新学习进度
```typescript
POST /api/progress/update
{
  "vocabulary_id": 1,
  "performance": {
    "correct": true,
    "response_time": 3.2,
    "difficulty_felt": "medium"
  }
}

Response: {
  "success": true,
  "data": {
    "updated_progress": {
      "mastery_level": 3,
      "next_review_at": "2024-01-18T10:00:00Z",
      "success_rate": 0.85
    }
  }
}
```

---

## 功能模块详细规格

### 5.1 用户认证模块

#### 5.1.1 功能概述
负责用户注册、登录、身份验证和权限管理。

#### 5.1.2 技术实现
```typescript
// 用户模型
interface User {
  id: number;
  email: string;
  username: string;
  password_hash: string;
  cefr_level: CEFRLevel;
  profile_picture?: string;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

// JWT Token结构
interface JWTPayload {
  user_id: number;
  username: string;
  cefr_level: CEFRLevel;
  iat: number;
  exp: number;
}

// 认证服务
class AuthService {
  async register(userData: RegisterData): Promise<AuthResult> {
    // 1. 验证邮箱格式和密码强度
    // 2. 检查邮箱是否已存在
    // 3. 加密密码
    // 4. 创建用户记录
    // 5. 生成JWT token
    // 6. 返回用户信息和token
  }
  
  async login(credentials: LoginData): Promise<AuthResult> {
    // 1. 验证用户凭据
    // 2. 验证密码
    // 3. 生成JWT token
    // 4. 更新最后登录时间
    // 5. 返回用户信息和token
  }
  
  async verifyToken(token: string): Promise<User> {
    // 1. 验证JWT token
    // 2. 检查token是否过期
    // 3. 返回用户信息
  }
}
```

#### 5.1.3 安全要求
- 密码使用bcrypt加密，salt rounds >= 12
- JWT token过期时间24小时
- 实现刷新token机制
- 登录失败超过5次锁定账户30分钟

### 5.2 英语水平评估模块

#### 5.2.1 功能概述
通过自适应测试评估用户英语水平，生成个性化学习计划。

#### 5.2.2 技术实现
```typescript
// 评估题目模型
interface AssessmentQuestion {
  id: number;
  word: string;
  options: string[];
  correct_index: number;
  difficulty: CEFRLevel;
  category: QuestionCategory;
  time_limit: number;
}

// 评估会话
interface AssessmentSession {
  id: string;
  user_id: number;
  questions: AssessmentQuestion[];
  answers: UserAnswer[];
  start_time: Date;
  current_difficulty: CEFRLevel;
  adaptive_algorithm: AdaptiveAlgorithm;
}

// 自适应算法
class AdaptiveAlgorithm {
  calculateNextDifficulty(currentLevel: CEFRLevel, isCorrect: boolean, responseTime: number): CEFRLevel {
    // 根据答题正确率和响应时间调整难度
    // 连续3题正确且响应时间<平均时间 -> 提升难度
    // 连续2题错误或响应时间>平均时间*2 -> 降低难度
  }
  
  determineUserLevel(answers: UserAnswer[]): AssessmentResult {
    // 基于IRT（Item Response Theory）模型
    // 计算用户能力值theta
    // 映射到CEFR等级
  }
}

// 评估服务
class AssessmentService {
  async startAssessment(userId: number): Promise<AssessmentSession> {
    // 1. 创建评估会话
    // 2. 选择初始难度题目(A2级别)
    // 3. 返回前5题
  }
  
  async submitAnswer(sessionId: string, answer: UserAnswer): Promise<NextQuestionResponse> {
    // 1. 记录答案
    // 2. 计算下一题难度
    // 3. 选择下一题目
    // 4. 判断是否完成评估
  }
  
  async completeAssessment(sessionId: string): Promise<AssessmentResult> {
    // 1. 计算最终CEFR等级
    // 2. 生成学习建议
    // 3. 更新用户资料
    // 4. 返回评估结果
  }
}
```

#### 5.2.3 算法规格
- 使用IRT模型进行能力评估
- 题目难度范围: A1-C2，共6个级别
- 评估题目数量: 15-25题（自适应调整）
- 准确率要求: 85%以上

### 5.3 词汇推荐模块

#### 5.3.1 功能概述
基于用户水平和学习进度，智能推荐每日学习词汇。

#### 5.3.2 技术实现
```typescript
// 词汇推荐算法
class VocabularyRecommendationEngine {
  async generateDailyVocabulary(userId: number, targetCount: number = 20): Promise<VocabularyRecommendation> {
    const user = await this.getUserProfile(userId);
    const progress = await this.getUserProgress(userId);
    
    // 推荐策略：
    // 70% 当前级别词汇
    // 20% 上一级别新词汇
    // 10% 需要复习的词汇
    
    const currentLevelWords = await this.getVocabularyByLevel(user.cefr_level, targetCount * 0.7);
    const nextLevelWords = await this.getVocabularyByLevel(this.getNextLevel(user.cefr_level), targetCount * 0.2);
    const reviewWords = await this.getWordsNeedingReview(userId, targetCount * 0.1);
    
    return {
      vocabulary: [...currentLevelWords, ...nextLevelWords, ...reviewWords],
      learning_plan: this.generateLearningPlan(user, progress)
    };
  }
  
  private async getVocabularyByLevel(level: CEFRLevel, count: number): Promise<Vocabulary[]> {
    // 1. 从缓存中获取该级别词汇
    // 2. 如果缓存不存在，从数据库查询
    // 3. 按频率排序选择词汇
    // 4. 缓存结果
  }
  
  private async getWordsNeedingReview(userId: number, count: number): Promise<Vocabulary[]> {
    // 1. 查询用户学习进度
    // 2. 根据遗忘曲线计算需要复习的词汇
    // 3. 按优先级排序
  }
}

// 词汇服务
class VocabularyService {
  async getDailyVocabulary(userId: number): Promise<VocabularyRecommendation> {
    const cacheKey = `daily_vocab:${userId}:${new Date().toISOString().split('T')[0]}`;
    
    // 尝试从缓存获取
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    // 生成推荐
    const recommendation = await this.recommendationEngine.generateDailyVocabulary(userId);
    
    // 缓存结果
    await this.redis.setex(cacheKey, 3600, JSON.stringify(recommendation));
    
    return recommendation;
  }
}
```

### 5.4 AI故事创建模块

#### 5.4.1 功能概述
核心模块，使用AI技术辅助用户创建包含目标词汇的故事。

#### 5.4.2 技术实现
```typescript
// 故事创建服务
class StoryCreationService {
  private openaiClient: OpenAI;
  
  async createStorySession(userId: number, request: CreateStoryRequest): Promise<StorySession> {
    // 1. 验证词汇列表
    // 2. 生成故事框架
    // 3. 创建会话记录
    // 4. 返回初始提示
    
    const vocabulary = await this.getVocabularyByIds(request.vocabulary_ids);
    const storyPrompt = await this.generateStoryPrompt(vocabulary, request.story_type, request.user_preferences);
    
    const session: StorySession = {
      id: this.generateSessionId(),
      user_id: userId,
      vocabulary_ids: request.vocabulary_ids,
      story_type: request.story_type,
      fragments: [],
      created_at: new Date()
    };
    
    await this.saveSession(session);
    
    return {
      session_id: session.id,
      vocabulary_to_use: vocabulary,
      story_prompt: storyPrompt
    };
  }
  
  async generateStoryFragment(sessionId: string, userInput: string, targetWords: string[]): Promise<StoryFragment> {
    const session = await this.getSession(sessionId);
    
    // 构建AI提示
    const prompt = this.buildAIPrompt(session, userInput, targetWords);
    
    // 调用OpenAI API
    const response = await this.openaiClient.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt()
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 200
    });
    
    const generatedText = response.choices[0].message.content;
    
    // 处理和验证生成的文本
    const fragment = await this.processGeneratedText(generatedText, userInput, targetWords);
    
    // 保存片段
    await this.saveFragment(sessionId, fragment);
    
    return fragment;
  }
  
  private getSystemPrompt(): string {
    return `你是一个英语学习助手，帮助用户创建有趣的故事来记忆英语单词。

规则：
1. 用户可能会提供中英文混合的句子
2. 你需要将其转换为地道的英文句子
3. 确保目标词汇在句子中使用正确
4. 保持故事的连贯性和趣味性
5. 如果用户的用法有误，提供友好的纠正建议

输出格式：
- generated_text: 生成的英文文本
- corrections: 如果有错误需要纠正的地方
- suggestions: 改进建议`;
  }
  
  private async processGeneratedText(text: string, userInput: string, targetWords: string[]): Promise<StoryFragment> {
    // 1. 解析AI响应
    // 2. 提取纠正建议
    // 3. 验证目标词汇使用
    // 4. 生成下一步提示
    
    return {
      user_input: userInput,
      generated_text: text,
      target_words: targetWords,
      corrections: this.extractCorrections(text, userInput),
      timestamp: new Date()
    };
  }
}

// AI提示模板
class PromptTemplate {
  static getStoryPrompt(vocabulary: Vocabulary[], storyType: string, preferences: UserPreferences): string {
    return `创建一个${storyType}类型的故事，需要使用以下词汇：
    
词汇列表：
${vocabulary.map(v => `- ${v.word}: ${v.definition_cn}`).join('\n')}

故事设置：
- 主角：${preferences.protagonist}
- 背景：${preferences.setting}

请开始故事的第一句话，并提示用户如何继续。`;
  }
  
  static getFragmentPrompt(context: StoryContext, userInput: string, targetWords: string[]): string {
    return `基于以下故事上下文，改进用户的输入：

故事上下文：
${context.previous_fragments.map(f => f.generated_text).join(' ')}

用户输入：
${userInput}

目标词汇：
${targetWords.join(', ')}

请：
1. 将用户输入转换为地道的英文
2. 确保目标词汇使用正确
3. 保持故事连贯性
4. 如有错误，提供纠正建议`;
  }
}
```

### 5.5 学习进度跟踪模块

#### 5.5.1 功能概述
跟踪用户学习进度，实现智能复习调度。

#### 5.5.2 技术实现
```typescript
// 遗忘曲线算法
class ForgettingCurveAlgorithm {
  calculateNextReviewTime(
    lastReviewTime: Date,
    masteryLevel: number,
    performance: ReviewPerformance
  ): Date {
    // 基于艾宾浩斯遗忘曲线
    // 复习间隔 = 基础间隔 * 掌握度系数 * 表现系数
    
    const baseInterval = [1, 3, 7, 14, 30, 90][masteryLevel] || 180; // 天数
    const masteryMultiplier = Math.pow(2, masteryLevel);
    const performanceMultiplier = performance.correct ? 1.3 : 0.6;
    
    const intervalDays = baseInterval * masteryMultiplier * performanceMultiplier;
    
    return new Date(lastReviewTime.getTime() + intervalDays * 24 * 60 * 60 * 1000);
  }
  
  updateMasteryLevel(currentLevel: number, performance: ReviewPerformance): number {
    if (performance.correct) {
      return Math.min(currentLevel + 1, 5);
    } else {
      return Math.max(currentLevel - 1, 0);
    }
  }
}

// 进度服务
class ProgressService {
  async updateProgress(userId: number, vocabularyId: number, performance: ReviewPerformance): Promise<UpdateResult> {
    const progress = await this.getUserProgress(userId, vocabularyId);
    
    // 更新掌握度
    const newMasteryLevel = this.forgettingCurve.updateMasteryLevel(progress.mastery_level, performance);
    
    // 计算下次复习时间
    const nextReviewTime = this.forgettingCurve.calculateNextReviewTime(
      new Date(),
      newMasteryLevel,
      performance
    );
    
    // 更新成功率
    const newSuccessRate = this.calculateSuccessRate(progress, performance);
    
    // 保存更新
    const updatedProgress = await this.saveProgress({
      ...progress,
      mastery_level: newMasteryLevel,
      last_reviewed_at: new Date(),
      next_review_at: nextReviewTime,
      review_count: progress.review_count + 1,
      success_rate: newSuccessRate
    });
    
    // 更新缓存
    await this.updateProgressCache(userId, vocabularyId, updatedProgress);
    
    return {
      updated_progress: updatedProgress,
      improvement: newMasteryLevel > progress.mastery_level,
      next_review_in_days: Math.ceil((nextReviewTime.getTime() - Date.now()) / (24 * 60 * 60 * 1000))
    };
  }
  
  async getProgressOverview(userId: number): Promise<ProgressOverview> {
    // 从缓存获取或计算统计数据
    const cacheKey = `progress_overview:${userId}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }
    
    const overview = await this.calculateProgressOverview(userId);
    
    // 缓存1小时
    await this.redis.setex(cacheKey, 3600, JSON.stringify(overview));
    
    return overview;
  }
}
```

---

## 用户界面规范

### 6.1 设计系统

#### 6.1.1 颜色规范
```css
:root {
  /* 主要颜色 */
  --primary-50: #f0f9ff;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* 辅助颜色 */
  --secondary-500: #6b7280;
  --accent-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* 文本颜色 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  
  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-muted: #f3f4f6;
}
```

#### 6.1.2 字体规范
```css
:root {
  /* 字体家族 */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'Fira Code', 'Consolas', monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
}
```

#### 6.1.3 间距规范
```css
:root {
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 0.75rem;  /* 12px */
  --spacing-4: 1rem;     /* 16px */
  --spacing-5: 1.25rem;  /* 20px */
  --spacing-6: 1.5rem;   /* 24px */
  --spacing-8: 2rem;     /* 32px */
  --spacing-10: 2.5rem;  /* 40px */
  --spacing-12: 3rem;    /* 48px */
  --spacing-16: 4rem;    /* 64px */
}
```

### 6.2 组件规范

#### 6.2.1 按钮组件
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  variant,
  size,
  disabled,
  loading,
  onClick,
  children
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors';
  
  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700',
    secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200',
    outline: 'border border-primary-300 text-primary-700 hover:bg-primary-50',
    ghost: 'text-primary-700 hover:bg-primary-50'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && <LoadingSpinner className="mr-2" />}
      {children}
    </button>
  );
};
```

#### 6.2.2 输入框组件
```typescript
interface InputProps {
  type?: 'text' | 'email' | 'password';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  label?: string;
}

const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  error,
  disabled,
  required,
  label
}) => {
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        className={`
          w-full px-3 py-2 border rounded-md text-sm
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
          ${error ? 'border-red-500' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
        `}
      />
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};
```

### 6.3 页面布局规范

#### 6.3.1 主页面布局
```typescript
const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};
```

#### 6.3.2 响应式设计
```css
/* 断点规范 */
@media (max-width: 640px) {
  /* 移动设备 */
  .container {
    padding: 1rem;
  }
  
  .sidebar {
    position: fixed;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  /* 平板设备 */
  .container {
    padding: 1.5rem;
  }
}

@media (min-width: 1025px) {
  /* 桌面设备 */
  .container {
    padding: 2rem;
  }
}
```

### 6.4 页面具体规范

#### 6.4.1 登录页面
```typescript
const LoginPage: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await authService.login(formData);
      // 跳转到主页
    } catch (error) {
      setErrors({ general: error.message });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">登录 StoryWord</h1>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            type="email"
            label="邮箱"
            value={formData.email}
            onChange={(value) => setFormData({...formData, email: value})}
            error={errors.email}
            required
          />
          
          <Input
            type="password"
            label="密码"
            value={formData.password}
            onChange={(value) => setFormData({...formData, password: value})}
            error={errors.password}
            required
          />
          
          {errors.general && (
            <div className="text-red-500 text-sm">{errors.general}</div>
          )}
          
          <Button
            type="submit"
            variant="primary"
            size="md"
            loading={loading}
            className="w-full"
          >
            登录
          </Button>
        </form>
        
        <div className="mt-4 text-center">
          <a href="/register" className="text-primary-600 hover:underline">
            没有账号？立即注册
          </a>
        </div>
      </div>
    </div>
  );
};
```

#### 6.4.2 单词学习页面
```typescript
const VocabularyLearningPage: React.FC = () => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [userSentence, setUserSentence] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [dailyVocabulary, setDailyVocabulary] = useState<Vocabulary[]>([]);
  const [completedWords, setCompletedWords] = useState<Set<number>>(new Set());

  const currentWord = dailyVocabulary[currentWordIndex];

  const handleSubmitSentence = async () => {
    if (!userSentence.trim() || !currentWord) return;

    setLoading(true);

    try {
      const response = await vocabularyService.processSentence({
        word: currentWord.word,
        userSentence: userSentence,
        wordId: currentWord.id
      });

      setAiResponse(response.correctedSentence);
      setCompletedWords(prev => new Set([...prev, currentWord.id]));

    } catch (error) {
      console.error('处理句子失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNextWord = () => {
    if (currentWordIndex < dailyVocabulary.length - 1) {
      setCurrentWordIndex(prev => prev + 1);
      setUserSentence('');
      setAiResponse('');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* 页面头部 - 进度指示 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">📚 每日单词学习</h1>
          <div className="text-sm text-gray-600">
            {currentWordIndex + 1} / {dailyVocabulary.length}
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentWordIndex + 1) / dailyVocabulary.length) * 100}%` }}
          />
        </div>
      </div>

      {/* 主学习区域 - 上下结构 */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* 单词展示区域 */}
        <div className="p-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          {currentWord && (
            <div className="text-center">
              <h2 className="text-4xl font-bold text-gray-900 mb-2">
                {currentWord.word}
              </h2>
              <p className="text-lg text-gray-600 mb-4">
                {currentWord.phonetic}
              </p>
              <div className="bg-white rounded-lg p-4 inline-block shadow-sm">
                <p className="text-xl text-gray-800 font-medium">
                  {currentWord.definition_cn}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* 用户输入区域 */}
        <div className="p-8">
          <label className="block text-lg font-medium text-gray-700 mb-4">
            💭 请用这个单词造一个句子：
          </label>
          <textarea
            value={userSentence}
            onChange={(e) => setUserSentence(e.target.value)}
            placeholder={`试着用 "${currentWord?.word}" 造一个句子...`}
            className="w-full h-32 p-4 border-2 border-gray-300 rounded-lg text-lg resize-none focus:border-blue-500 focus:outline-none"
            disabled={loading}
          />

          <div className="flex justify-between items-center mt-6">
            <button
              onClick={handleSubmitSentence}
              disabled={!userSentence.trim() || loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '处理中...' : '✨ 提交句子'}
            </button>

            {aiResponse && (
              <button
                onClick={handleNextWord}
                className="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700"
              >
                下一个单词 →
              </button>
            )}
          </div>
        </div>

        {/* AI反馈展示区域 */}
        {aiResponse && (
          <div className="p-8 bg-gray-50 border-t">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              🤖 AI 修改建议：
            </h3>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <p className="text-lg text-gray-800 leading-relaxed">
                {aiResponse}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
```

---

## 开发规范

### 7.1 代码规范

#### 7.1.1 TypeScript 规范
```typescript
// 类型定义规范
interface User {
  readonly id: number;
  email: string;
  username: string;
  createdAt: Date;
  updatedAt: Date;
}

// 枚举定义
enum CEFRLevel {
  A1 = 'A1',
  A2 = 'A2',
  B1 = 'B1',
  B2 = 'B2',
  C1 = 'C1',
  C2 = 'C2'
}

// 函数定义规范
const getUserById = async (id: number): Promise<User | null> => {
  try {
    const user = await userRepository.findById(id);
    return user;
  } catch (error) {
    logger.error('Error fetching user:', error);
    throw new Error('User not found');
  }
};
```

#### 7.1.2 React 组件规范
```typescript
// 组件props接口
interface ComponentProps {
  title: string;
  description?: string;
  onSubmit: (data: FormData) => void;
  children?: React.ReactNode;
}

// 组件定义
const MyComponent: React.FC<ComponentProps> = ({
  title,
  description,
  onSubmit,
  children
}) => {
  // Hook必须在组件顶部
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 事件处理函数
  const handleSubmit = useCallback((data: FormData) => {
    setLoading(true);
    setError(null);
    
    try {
      onSubmit(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [onSubmit]);
  
  // 渲染
  return (
    <div className="component-container">
      <h1>{title}</h1>
      {description && <p>{description}</p>}
      {error && <ErrorMessage message={error} />}
      {children}
    </div>
  );
};
```

#### 7.1.3 API 路由规范
```typescript
// 路由定义
router.post('/api/users', validateRequest(createUserSchema), async (req, res) => {
  try {
    const userData = req.body;
    const user = await userService.createUser(userData);
    
    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: {
        code: 'USER_CREATION_FAILED',
        message: error.message
      }
    });
  }
});

// 请求验证中间件
const validateRequest = (schema: Joi.Schema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: error.details[0].message
        }
      });
    }
    
    next();
  };
};
```

### 7.2 错误处理规范

#### 7.2.1 错误类型定义
```typescript
// 自定义错误类
class AppError extends Error {
  constructor(
    public code: string,
    public message: string,
    public statusCode: number = 500,
    public isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类
class UserNotFoundError extends AppError {
  constructor(userId: number) {
    super('USER_NOT_FOUND', `User with ID ${userId} not found`, 404);
  }
}

class ValidationError extends AppError {
  constructor(field: string, message: string) {
    super('VALIDATION_ERROR', `${field}: ${message}`, 400);
  }
}
```

#### 7.2.2 全局错误处理
```typescript
// 错误处理中间件
const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('Unhandled error:', error);
  
  if (error instanceof AppError) {
    return res.status(error.statusCode).json({
      success: false,
      error: {
        code: error.code,
        message: error.message
      }
    });
  }
  
  // 未知错误
  return res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred'
    }
  });
};
```

### 7.3 日志规范

#### 7.3.1 日志配置
```typescript
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

#### 7.3.2 日志使用规范
```typescript
// 服务层日志
class UserService {
  async createUser(userData: CreateUserData): Promise<User> {
    logger.info('Creating user', { email: userData.email });
    
    try {
      const user = await userRepository.create(userData);
      logger.info('User created successfully', { userId: user.id });
      return user;
    } catch (error) {
      logger.error('Failed to create user', { error: error.message, userData });
      throw error;
    }
  }
}

// 控制器层日志
const createUserController = async (req: Request, res: Response) => {
  const requestId = req.headers['x-request-id'];
  
  logger.info('Create user request', { requestId, body: req.body });
  
  try {
    const user = await userService.createUser(req.body);
    
    logger.info('Create user success', { requestId, userId: user.id });
    
    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    logger.error('Create user failed', { requestId, error: error.message });
    
    res.status(500).json({
      success: false,
      error: {
        code: 'USER_CREATION_FAILED',
        message: error.message
      }
    });
  }
};
```

---

## 测试要求

### 8.1 测试策略

#### 8.1.1 测试金字塔
```
    /\
   /  \
  /    \
 /  E2E  \  <- 10% (端到端测试)
/________\
\        /
 \      /
  \    /
   \  /    <- 20% (集成测试)
    \/
____________
\          /
 \        /
  \      /
   \    /    <- 70% (单元测试)
    \  /
     \/
```

#### 8.1.2 测试覆盖率要求
- 单元测试覆盖率: >= 80%
- 集成测试覆盖率: >= 60%
- E2E测试覆盖率: >= 40%

### 8.2 单元测试规范

#### 8.2.1 测试框架配置
```typescript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/index.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

#### 8.2.2 测试用例示例
```typescript
// userService.test.ts
describe('UserService', () => {
  let userService: UserService;
  let mockUserRepository: jest.Mocked<UserRepository>;
  
  beforeEach(() => {
    mockUserRepository = {
      findById: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    } as any;
    
    userService = new UserService(mockUserRepository);
  });
  
  describe('createUser', () => {
    it('should create user successfully', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123'
      };
      
      const expectedUser = {
        id: 1,
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      mockUserRepository.create.mockResolvedValue(expectedUser);
      
      // Act
      const result = await userService.createUser(userData);
      
      // Assert
      expect(result).toEqual(expectedUser);
      expect(mockUserRepository.create).toHaveBeenCalledWith(userData);
    });
    
    it('should throw error if email already exists', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123'
      };
      
      mockUserRepository.create.mockRejectedValue(
        new Error('Email already exists')
      );
      
      // Act & Assert
      await expect(userService.createUser(userData))
        .rejects
        .toThrow('Email already exists');
    });
  });
});
```

### 8.3 集成测试规范

#### 8.3.1 API集成测试
```typescript
// userController.integration.test.ts
describe('User Controller Integration', () => {
  let app: Express;
  let testDb: TestDatabase;
  
  beforeAll(async () => {
    testDb = await TestDatabase.create();
    app = createApp(testDb.connection);
  });
  
  afterAll(async () => {
    await testDb.cleanup();
  });
  
  beforeEach(async () => {
    await testDb.seed();
  });
  
  describe('POST /api/users', () => {
    it('should create user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123'
      };
      
      const response = await request(app)
        .post('/api/users')
        .send(userData)
        .expect(201);
      
      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: expect.any(Number),
          email: userData.email,
          username: userData.username
        }
      });
      
      // 验证数据库中的记录
      const user = await testDb.query('SELECT * FROM users WHERE email = $1', [userData.email]);
      expect(user.rows).toHaveLength(1);
    });
  });
});
```

### 8.4 E2E测试规范

#### 8.4.1 Playwright配置
```typescript
// playwright.config.ts
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    }
  ]
});
```

#### 8.4.2 E2E测试用例
```typescript
// e2e/user-journey.spec.ts
import { test, expect } from '@playwright/test';

test.describe('User Learning Journey', () => {
  test('complete user flow from registration to story creation', async ({ page }) => {
    // 1. 注册新用户
    await page.goto('/register');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="register-button"]');
    
    // 2. 完成英语水平评估
    await expect(page).toHaveURL('/assessment');
    
    // 模拟完成评估
    for (let i = 0; i < 15; i++) {
      await page.click('[data-testid="option-0"]');
      await page.click('[data-testid="next-button"]');
    }
    
    // 3. 进入主页面
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-level"]')).toContainText('B1');
    
    // 4. 开始每日学习
    await page.click('[data-testid="start-learning-button"]');
    
    // 5. 创建故事
    await expect(page).toHaveURL('/story/create');
    await page.fill('[data-testid="story-input"]', 'The student wanted to 发展 his skills');
    await page.click('[data-testid="generate-button"]');
    
    // 6. 验证故事生成
    await expect(page.locator('[data-testid="generated-story"]')).toBeVisible();
    await expect(page.locator('[data-testid="generated-story"]')).toContainText('develop');
    
    // 7. 完成学习会话
    await page.click('[data-testid="complete-session-button"]');
    
    // 8. 查看学习进度
    await expect(page).toHaveURL('/progress');
    await expect(page.locator('[data-testid="words-learned"]')).toContainText('5');
  });
});
```

---

## 部署配置

### 9.1 Docker配置

#### 9.1.1 Dockerfile
```dockerfile
# 前端构建
FROM node:18-alpine AS frontend-build
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ .
RUN npm run build

# 后端构建
FROM node:18-alpine AS backend-build
WORKDIR /app/backend
COPY backend/package*.json ./
RUN npm ci --only=production
COPY backend/ .
RUN npm run build

# 生产环境
FROM node:18-alpine AS production
WORKDIR /app

# 复制后端构建结果
COPY --from=backend-build /app/backend/dist ./dist
COPY --from=backend-build /app/backend/node_modules ./node_modules
COPY --from=backend-build /app/backend/package.json ./

# 复制前端构建结果
COPY --from=frontend-build /app/frontend/dist ./public

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

EXPOSE 3000
CMD ["node", "dist/server.js"]
```

#### 9.1.2 docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/storyword
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=storyword
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d storyword"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
```

### 9.2 环境配置

#### 9.2.1 环境变量配置
```bash
# .env.production
NODE_ENV=production
PORT=3000

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/storyword
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=500

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 安全配置
CORS_ORIGIN=https://storyword.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 监控配置
SENTRY_DSN=your-sentry-dsn
ANALYTICS_ID=your-analytics-id
```

#### 9.2.2 配置管理
```typescript
// config/index.ts
import dotenv from 'dotenv';

dotenv.config();

export const config = {
  app: {
    port: parseInt(process.env.PORT || '3000'),
    env: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || '*'
  },
  database: {
    url: process.env.DATABASE_URL || 'postgresql://localhost:5432/storyword',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    ssl: process.env.NODE_ENV === 'production'
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    keyPrefix: 'storyword:'
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback-secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    model: process.env.OPENAI_MODEL || 'gpt-4',
    maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '500')
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    dir: process.env.LOG_DIR || './logs'
  }
};

// 验证必需的环境变量
const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'OPENAI_API_KEY'
];

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
});
```

### 9.3 部署流程

#### 9.3.1 CI/CD配置 (GitHub Actions)
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret
        OPENAI_API_KEY: test-key
    
    - name: Run E2E tests
      run: npm run test:e2e
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: storyword/app:latest
    
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.7
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /app
          docker-compose pull
          docker-compose down
          docker-compose up -d
          docker system prune -f
```

#### 9.3.2 健康检查配置
```typescript
// health.ts
import { Request, Response } from 'express';
import { Pool } from 'pg';
import Redis from 'ioredis';

interface HealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy';
  message?: string;
  timestamp: string;
}

export const healthCheck = async (req: Request, res: Response) => {
  const checks: HealthCheck[] = [];
  
  // 数据库健康检查
  try {
    const pool = new Pool({ connectionString: process.env.DATABASE_URL });
    await pool.query('SELECT 1');
    checks.push({
      service: 'database',
      status: 'healthy',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    checks.push({
      service: 'database',
      status: 'unhealthy',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
  
  // Redis健康检查
  try {
    const redis = new Redis(process.env.REDIS_URL);
    await redis.ping();
    checks.push({
      service: 'redis',
      status: 'healthy',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    checks.push({
      service: 'redis',
      status: 'unhealthy',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
  
  // OpenAI API健康检查
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      }
    });
    
    if (response.ok) {
      checks.push({
        service: 'openai',
        status: 'healthy',
        timestamp: new Date().toISOString()
      });
    } else {
      throw new Error(`OpenAI API returned ${response.status}`);
    }
  } catch (error) {
    checks.push({
      service: 'openai',
      status: 'unhealthy',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
  
  const allHealthy = checks.every(check => check.status === 'healthy');
  
  res.status(allHealthy ? 200 : 503).json({
    status: allHealthy ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date().toISOString()
  });
};
```

---

## 开发时间线

### 10.1 开发阶段规划

#### 第一阶段 (Week 1-2): 基础架构
- [ ] 项目初始化和环境配置
- [ ] 数据库设计和迁移脚本
- [ ] 基础认证系统
- [ ] Docker配置和部署脚本

#### 第二阶段 (Week 3-4): 核心功能开发
- [ ] 英语水平评估系统
- [ ] 词汇推荐算法
- [ ] 基础UI组件库
- [ ] 用户管理系统

#### 第三阶段 (Week 5-6): AI集成
- [ ] OpenAI API集成
- [ ] 故事生成引擎
- [ ] 故事创建用户界面
- [ ] 错误处理和优化

#### 第四阶段 (Week 7-8): 学习系统
- [ ] 学习进度跟踪
- [ ] 智能复习算法
- [ ] 统计分析功能
- [ ] 用户反馈系统

#### 第五阶段 (Week 9-10): 测试和优化
- [ ] 单元测试覆盖
- [ ] 集成测试
- [ ] E2E测试
- [ ] 性能优化

#### 第六阶段 (Week 11-12): 发布准备
- [ ] 生产环境部署
- [ ] 监控和日志系统
- [ ] 用户文档
- [ ] 上线准备

### 10.2 里程碑验收标准

#### 里程碑1: 用户可以完成注册和水平评估
- [ ] 用户注册功能正常
- [ ] 英语水平评估准确率>85%
- [ ] 基础用户界面完整

#### 里程碑2: 用户可以创建第一个故事
- [ ] AI故事生成功能正常
- [ ] 词汇推荐算法有效
- [ ] 故事创建界面友好

#### 里程碑3: 完整的学习闭环
- [ ] 学习进度跟踪准确
- [ ] 复习系统有效
- [ ] 用户体验流畅

#### 里程碑4: 生产环境就绪
- [ ] 所有测试通过
- [ ] 性能指标达标
- [ ] 部署流程自动化

---

## 附录

### A. 开发工具推荐

#### A.1 必需工具
- **IDE**: Visual Studio Code
- **数据库工具**: DBeaver
- **API测试**: Postman
- **版本控制**: Git
- **包管理**: npm/yarn

#### A.2 推荐插件
- **VSCode插件**:
  - TypeScript Hero
  - Prettier
  - ESLint
  - Auto Rename Tag
  - Bracket Pair Colorizer

#### A.3 调试工具
- **Chrome DevTools**: 前端调试
- **Node.js Inspector**: 后端调试
- **Redis Commander**: Redis数据查看

### B. 参考资料

#### B.1 技术文档
- [React Documentation](https://react.dev/)
- [Node.js Documentation](https://nodejs.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

#### B.2 最佳实践
- [TypeScript Best Practices](https://typescript-eslint.io/docs/)
- [REST API Design Guidelines](https://restfulapi.net/)
- [React Testing Best Practices](https://testing-library.com/docs/react-testing-library/intro/)

---

**文档结束**

> 本文档为StoryWord MVP产品的完整开发指南，包含了从技术架构到部署的所有必需信息。开发团队应严格按照本文档的规范进行开发，确保产品质量和开发效率。

> 如有任何疑问或需要澄清的地方，请及时与产品团队沟通。